{"name": "dashboard", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"axios": "^1.9.0", "earth-flyline": "^1.5.8", "echarts": "^5.6.0", "echarts-gl": "^2.0.9", "three": "^0.176.0", "vue": "^3.5.13", "vue-router": "^4.5.1"}, "devDependencies": {"@vitejs/plugin-vue": "^5.2.3", "less": "^4.3.0", "vite": "^6.3.5", "vite-plugin-svg-icons": "^2.0.1"}}